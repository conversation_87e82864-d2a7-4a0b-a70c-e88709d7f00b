import { Request, Response, NextFunction } from 'express'
 
export default function lastModifiedHeader(req: Request, res: Response, next: NextFunction) {
  const originalSend = res.send
  const originalJson = res.json
 
  
  res.send = function(body) {
    let modifiedTime = new Date();
    if (res.statusCode === 200 || res.statusCode === 204) {
      //console.log("body = ", req.body)
      if (req.body != undefined) {
       // console.log("req.body.object = ", req.body.object)
        if (req.body.stored != undefined) {
          console.log("req.body.stored = ", req.body.stored)
          modifiedTime = new Date(req.body.stored)

          // Use the modification time from res.locals if available, otherwise current time
      //modifiedTime = res.locals.lastModified
      console.log("modifiedTime = ", modifiedTime)
      /* res.setHeader('Last-Modified', modifiedTime.toUTCString()) */
      res.setHeader('Last-Modified', modifiedTime.toISOString())
        
        }
      }
      // Use the modification time from res.locals if available, otherwise current time
     // const modifiedTime = res.locals.lastModified
      //console.log("modifiedTime = ", res.locals.lastModified)
      /* res.setHeader('Last-Modified', modifiedTime.toUTCString()) */
     // res.setHeader('Last-Modified', modifiedTime.toISOString())
    }
    return originalSend.call(this, body)
  }
 
  res.json = function(body) {

if (res.statusCode === 200 || res.statusCode === 204) {
      console.log("body = ", req.body)
      if (req.body != undefined) {
        console.log("req.body.object = ", req.body.object)
        if (req.body.stored != undefined) {
          console.log("req.body.stored = ", req.body.stored)
          res.locals.lastModified = new Date(req.body.stored)

          // Use the modification time from res.locals if available, otherwise current time
      const modifiedTime = res.locals.lastModified
      console.log("modifiedTime = ", res.locals.lastModified)
      /* res.setHeader('Last-Modified', modifiedTime.toUTCString()) */
      res.setHeader('Last-Modified', modifiedTime.toISOString())
        }
        
        
      }
      
    }


    //if (res.statusCode === 200 || res.statusCode === 204) {
      // Use the modification time from res.locals if available, otherwise current time
     // const modifiedTime = res.locals.lastModified
    //  res.setHeader('Last-Modified', modifiedTime.toISOString())
   // }
    return originalJson.call(this, body)
  }
 
  next()
}